<?php
// --- MySQL Connection Setup ---
$host = "localhost";       // your DB host
$user = "root";            // your DB username
$pass = "";                // your DB password
$db   = "test_db";         // your DB name

$conn = null;
$db_connected = false;

try {
    $conn = new mysqli($host, $user, $pass, $db);

    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    $db_connected = true;
    echo "<div style='color: green; margin-bottom: 10px;'>✓ Database connected successfully!</div>";
} catch (Exception $e) {
    echo "<div style='color: red; margin-bottom: 10px;'>⚠ Database connection failed: " . $e->getMessage() . "</div>";
    echo "<div style='color: orange; margin-bottom: 10px;'>Note: The form will still work, but data won't be saved to database.</div>";
}

// --- When form is submitted ---
if (isset($_SERVER["REQUEST_METHOD"]) && $_SERVER["REQUEST_METHOD"] === "POST") {
    // Get inputs
    $a = (int)$_POST["a"];
    $b = (int)$_POST["b"];
    $c = $_POST["c"];

    // Insert into MySQL (only if connected)
    if ($db_connected && $conn) {
        try {
            $stmt = $conn->prepare("INSERT INTO inputs (a, b, c) VALUES (?, ?, ?)");
            $stmt->bind_param("iis", $a, $b, $c);
            $stmt->execute();
            echo "<div style='color: green; margin-bottom: 10px;'>✓ Data saved to database successfully!</div>";
        } catch (Exception $e) {
            echo "<div style='color: red; margin-bottom: 10px;'>⚠ Failed to save to database: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div style='color: orange; margin-bottom: 10px;'>⚠ Data not saved (database not connected)</div>";
    }

    // Compute sum
    $sum = $a + $b;

    // Reverse string manually (no built-in functions)
    $reversed = "";
    $len = 0;
    while (isset($c[$len])) $len++; // manually get length
    for ($i = $len - 1; $i >= 0; $i--) {
        $reversed .= $c[$i];
    }

    // Show result
    echo "<h3>Result:</h3>";
    echo "Sum of a + b: <strong>$sum</strong><br>";
    echo "Reversed string c: <strong>$reversed</strong><br><br>";
}
?>

<!-- HTML Form -->
<form method="POST" action="">
    <label>Integer A:</label><br>
    <input type="number" name="a" required><br><br>

    <label>Integer B:</label><br>
    <input type="number" name="b" required><br><br>

    <label>String C:</label><br>
    <input type="text" name="c" required><br><br>

    <button type="submit">Submit</button>
</form>
