<?php
// --- MySQL Connection Setup ---
$host = "localhost";       // your DB host
$user = "root";            // your DB username
$pass = "";                // your DB password
$db   = "test_db";         // your DB name

$conn = new mysqli($host, $user, $pass, $db);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// --- When form is submitted ---
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Get inputs
    $a = (int)$_POST["a"];
    $b = (int)$_POST["b"];
    $c = $_POST["c"];

    // Insert into MySQL
    $stmt = $conn->prepare("INSERT INTO inputs (a, b, c) VALUES (?, ?, ?)");
    $stmt->bind_param("iis", $a, $b, $c);
    $stmt->execute();

    // Compute sum
    $sum = $a + $b;

    // Reverse string manually (no built-in functions)
    $reversed = "";
    $len = 0;
    while (isset($c[$len])) $len++; // manually get length
    for ($i = $len - 1; $i >= 0; $i--) {
        $reversed .= $c[$i];
    }

    // Show result
    echo "<h3>Result:</h3>";
    echo "Sum of a + b: <strong>$sum</strong><br>";
    echo "Reversed string c: <strong>$reversed</strong><br><br>";
}
?>

<!-- HTML Form -->
<form method="POST" action="">
    <label>Integer A:</label><br>
    <input type="number" name="a" required><br><br>

    <label>Integer B:</label><br>
    <input type="number" name="b" required><br><br>

    <label>String C:</label><br>
    <input type="text" name="c" required><br><br>

    <button type="submit">Submit</button>
</form>
